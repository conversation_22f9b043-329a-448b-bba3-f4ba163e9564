<?php

declare(strict_types=1);

use <PERSON>\Config\RectorConfig;
use <PERSON>\Set\ValueObject\SetList;
use <PERSON>\ValueObject\PhpVersion;
use <PERSON><PERSON><PERSON><PERSON>\Set\LaravelLevelSetList;
use <PERSON><PERSON><PERSON><PERSON>\Set\LaravelSetList;

return RectorConfig::configure()
    ->withPaths([
        __DIR__.'/app',
        __DIR__.'/database',
        // __DIR__ . '/bootstrap',
        // __DIR__ . '/config',
        // __DIR__ . '/public',
        // __DIR__ . '/resources',
        __DIR__.'/routes',
        __DIR__.'/tests',
    ])
    // uncomment to reach your current PHP version
    // ->withPhpSets()
    ->withPhpVersion(PhpVersion::PHP_83)
    ->withSets([
        SetList::CODE_QUALITY,
        LaravelLevelSetList::UP_TO_LARAVEL_120,
        LaravelSetList::LARAVEL_LEGACY_FACTORIES_TO_CLASSES,
        LaravelSetList::LARAVEL_CONTAINER_STRING_TO_FULLY_QUALIFIED_NAME,
        LaravelSetList::LARAVEL_ARRAY_STR_FUNCTION_TO_STATIC_CALL,
        LaravelSetList::LARAVEL_COLLECTION,
        LaravelSetList::LARAVEL_IF_HELPERS,
    ]);
// ->withTypeCoverageLevel(1)
// ->withDeadCodeLevel(1)
// ->withCodeQualityLevel(1)
// ->withImportNames(removeUnusedImports: true);
