{"__meta": {"id": "01JW2RD9ZFEXZ6DCNYRBETH5FS", "datetime": "2025-05-25 03:40:00", "utime": **********.38788, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748144395.830713, "end": **********.387903, "duration": 4.55718994140625, "duration_str": "4.56s", "measures": [{"label": "Booting", "start": 1748144395.830713, "relative_start": 0, "end": **********.419374, "relative_end": **********.419374, "duration": 1.****************, "duration_str": "1.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.41939, "relative_start": 1.***************, "end": **********.387907, "relative_end": 4.0531158447265625e-06, "duration": 2.****************, "duration_str": "2.97s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.117579, "relative_start": 2.****************, "end": **********.230603, "relative_end": **********.230603, "duration": 0.*****************, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.853895, "relative_start": 3.***************, "end": **********.264822, "relative_end": **********.264822, "duration": 1.****************, "duration_str": "1.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: front/home/<USER>", "start": **********.11446, "relative_start": 4.***************, "end": **********.11446, "relative_end": **********.11446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "10MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.4.7", "Environment": "local", "Debug Mode": "Enabled", "URL": "manajement-magang.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "front/home/<USER>", "param_count": null, "params": [], "start": **********.114384, "type": "tsx", "hash": "tsxE:\\Coding\\PHP\\Laravel\\manajement-magang\\resources\\js/Pages/front/home/<USER>/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FCoding%2FPHP%2FLaravel%2Fmanajement-magang%2Fresources%2Fjs%2Fpages%2Ffront%2Fhome%2Findex.tsx&line=1", "ajax": false, "filename": "index.tsx", "line": "?"}}]}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.31139000000000006, "accumulated_duration_str": "311ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'zklmUmLUyrTzl0zxtWgIysPaJWl4KW2WltPTMS4w' limit 1", "type": "query", "params": [], "bindings": ["zklmUmLUyrTzl0zxtWgIysPaJWl4KW2WltPTMS4w"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.373133, "duration": 0.06459000000000001, "duration_str": "64.59ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FCoding%2FPHP%2FLaravel%2Fmanajement-magang%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "manajement-magang", "explain": null, "start_percent": 0, "width_percent": 20.742}, {"sql": "select * from `global_variables` where `is_active` = 1 and `global_variables`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 60}, {"index": 16, "namespace": "middleware", "name": "auth", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 83}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/HandleViewPreference.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\app\\Http\\Middleware\\HandleViewPreference.php", "line": 18}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.581936, "duration": 0.08098000000000001, "duration_str": "80.98ms", "memory": 0, "memory_str": null, "filename": "HandleInertiaRequests.php:60", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FCoding%2FPHP%2FLaravel%2Fmanajement-magang%2Fapp%2FHttp%2FMiddleware%2FHandleInertiaRequests.php&line=60", "ajax": false, "filename": "HandleInertiaRequests.php", "line": "60"}, "connection": "manajement-magang", "explain": null, "start_percent": 20.742, "width_percent": 26.006}, {"sql": "select * from `faqs` where `is_active` = 1 and `faqs`.`deleted_at` is null order by `order` asc, `created_at` desc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\app\\Http\\Controllers\\HomeController.php", "line": 19}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.6677282, "duration": 0.16582, "duration_str": "166ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:19", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "E:\\Coding\\PHP\\Laravel\\manajement-magang\\app\\Http\\Controllers\\HomeController.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FCoding%2FPHP%2FLaravel%2Fmanajement-magang%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=19", "ajax": false, "filename": "HomeController.php", "line": "19"}, "connection": "manajement-magang", "explain": null, "start_percent": 46.748, "width_percent": 53.252}]}, "models": {"data": {"App\\Models\\Faq": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FCoding%2FPHP%2FLaravel%2Fmanajement-magang%2Fapp%2FModels%2FFaq.php&line=1", "ajax": false, "filename": "Faq.php", "line": "?"}}, "App\\Models\\GlobalVariable": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FCoding%2FPHP%2FLaravel%2Fmanajement-magang%2Fapp%2FModels%2FGlobalVariable.php&line=1", "ajax": false, "filename": "GlobalVariable.php", "line": "?"}}}, "count": 27, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://manajement-magang.test", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=E%3A%2FCoding%2FPHP%2FLaravel%2Fmanajement-magang%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=14\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FCoding%2FPHP%2FLaravel%2Fmanajement-magang%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=14\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/HomeController.php:14-28</a>", "middleware": "web", "telescope": "<a href=\"http://manajement-magang.test/_debugbar/telescope/9efe2899-ea03-4ea8-91e4-e8bda6b6c0c0\" target=\"_blank\">View in Telescope</a>", "duration": "3.89s", "peak_memory": "14MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1893616704 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1893616704\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-222656544 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-222656544\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1939852045 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"325 characters\">sidebar_state=true; appearance=dark; internships_view_preference=eyJpdiI6IjJNaHMzOEh1QlU5OUU3OFk2MXd1Y2c9PSIsInZhbHVlIjoielJZNnoxTnQvN2xzRldpa3FnTUhSZDZxNTFVQ0czTUVWaGZmWTFvYXAzV2piWUYrNlV2b0ErcFI1dUlZcFJQWiIsIm1hYyI6IjVjN2M2YjAyYmQyMjJkZWI5ZTI2Njg0MGE5ZjI1MDdiNTk5Nzk0YjcyYjljNDZiN2FjNGEyZGVkZWI1Y2IyYWUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,id;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">manajement-magang.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939852045\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-948968183 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"4 characters\">dark</span>\"\n  \"<span class=sf-dump-key>internships_view_preference</span>\" => \"<span class=sf-dump-str title=\"5 characters\">cards</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-948968183\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1548338306 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 25 May 2025 03:40:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1548338306\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1171667448 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crr4bvuOcsBPj86k8pLNCJcJRNHa6vqHIZDyDcbT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171667448\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://manajement-magang.test", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index"}, "badge": null}}