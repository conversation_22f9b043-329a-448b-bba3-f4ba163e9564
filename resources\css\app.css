@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source "../views";
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }

    html,
    body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        overflow-x: hidden;
    }
}

:root {
    --background: rgb(255, 255, 255);
    --foreground: rgb(30, 41, 59);
    --card: rgb(255, 255, 255);
    --card-foreground: rgb(30, 41, 59);
    --popover: rgb(255, 255, 255);
    --popover-foreground: rgb(30, 41, 59);
    --primary: rgb(59, 130, 246);
    --primary-foreground: rgb(255, 255, 255);
    --secondary: rgb(240, 244, 248);
    --secondary-foreground: rgb(30, 41, 59);
    --muted: rgb(148, 163, 184);
    --muted-foreground: rgb(100, 116, 139);
    --accent: rgb(59, 130, 246);
    --accent-foreground: rgb(255, 255, 255);
    --destructive: rgb(239, 68, 68);
    --destructive-foreground: rgb(255, 255, 255);
    --border: rgb(226, 232, 240);
    --input: rgb(240, 244, 248);
    --ring: rgb(59, 130, 246);
    --chart-1: rgb(59, 130, 246);
    --chart-2: rgb(139, 92, 246);
    --chart-3: rgb(236, 72, 153);
    --chart-4: rgb(244, 114, 182);
    --chart-5: rgb(34, 197, 94);
    --sidebar: rgb(240, 244, 248);
    --sidebar-foreground: rgb(30, 41, 59);
    --sidebar-primary: rgb(59, 130, 246);
    --sidebar-primary-foreground: rgb(255, 255, 255);
    --sidebar-accent: rgb(59, 130, 246);
    --sidebar-accent-foreground: rgb(255, 255, 255);
    --sidebar-border: rgb(226, 232, 240);
    --sidebar-ring: rgb(59, 130, 246);
    --font-sans: sans-serif;
    --font-serif: serif;
    --font-mono: monospace;
    --radius: 0.75rem;
    --shadow-2xs: 0px 0px 20px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 0px 20px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 0px 20px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow: 0px 0px 20px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md: 0px 0px 20px 0px hsl(0 0% 0% / 0.1), 0px 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0px 0px 20px 0px hsl(0 0% 0% / 0.1), 0px 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl: 0px 0px 20px 0px hsl(0 0% 0% / 0.1), 0px 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0px 0px 20px 0px hsl(0 0% 0% / 0.25);
    --tracking-normal: 0.025em;
}

.dark {
    --background: rgb(15, 23, 42);
    --foreground: rgb(255, 255, 255);
    --card: rgb(30, 41, 59);
    --card-foreground: rgb(255, 255, 255);
    --popover: rgb(30, 41, 59);
    --popover-foreground: rgb(255, 255, 255);
    --primary: rgb(59, 130, 246);
    --primary-foreground: rgb(255, 255, 255);
    --secondary: rgb(30, 41, 59);
    --secondary-foreground: rgb(255, 255, 255);
    --muted: rgb(71, 85, 105);
    --muted-foreground: rgb(148, 163, 184);
    --accent: rgb(37, 99, 235);
    --accent-foreground: rgb(255, 255, 255);
    --destructive: rgb(239, 68, 68);
    --destructive-foreground: rgb(255, 255, 255);
    --border: rgb(30, 41, 59);
    --input: rgb(30, 41, 59);
    --ring: rgb(37, 99, 235);
    --chart-1: rgb(37, 99, 235);
    --chart-2: rgb(139, 92, 246);
    --chart-3: rgb(236, 72, 153);
    --chart-4: rgb(244, 114, 182);
    --chart-5: rgb(34, 197, 94);
    --sidebar: rgb(30, 41, 59);
    --sidebar-foreground: rgb(255, 255, 255);
    --sidebar-primary: rgb(59, 130, 246);
    --sidebar-primary-foreground: rgb(255, 255, 255);
    --sidebar-accent: rgb(37, 99, 235);
    --sidebar-accent-foreground: rgb(255, 255, 255);
    --sidebar-border: rgb(51, 65, 85);
    --sidebar-ring: rgb(37, 99, 235);
    --font-sans: sans-serif;
    --font-serif: serif;
    --font-mono: monospace;
    --radius: 0.75rem;
    --shadow-2xs: 0px 0px 20px 0px hsl(0 0% 0% / 0.1);
    --shadow-xs: 0px 0px 20px 0px hsl(0 0% 0% / 0.1);
    --shadow-sm: 0px 0px 20px 0px hsl(0 0% 0% / 0.2), 0px 1px 2px -1px hsl(0 0% 0% / 0.2);
    --shadow: 0px 0px 20px 0px hsl(0 0% 0% / 0.2), 0px 1px 2px -1px hsl(0 0% 0% / 0.2);
    --shadow-md: 0px 0px 20px 0px hsl(0 0% 0% / 0.2), 0px 2px 4px -1px hsl(0 0% 0% / 0.2);
    --shadow-lg: 0px 0px 20px 0px hsl(0 0% 0% / 0.2), 0px 4px 6px -1px hsl(0 0% 0% / 0.2);
    --shadow-xl: 0px 0px 20px 0px hsl(0 0% 0% / 0.2), 0px 8px 10px -1px hsl(0 0% 0% / 0.2);
    --shadow-2xl: 0px 0px 20px 0px hsl(0 0% 0% / 0.5);
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);

    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);

    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-normal: var(--tracking-normal);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
    letter-spacing: var(--tracking-normal);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/* Global Scrollbar Styles */
::-webkit-scrollbar {
    width: 10px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--color-secondary);
}

.dark ::-webkit-scrollbar-track {
    background: var(--secondary); /* .dark uses --secondary directly */
}

::-webkit-scrollbar-thumb {
    background-color: var(--color-primary);
    border-radius: 10px;
    border: 2px solid var(--color-secondary);
}

.dark ::-webkit-scrollbar-thumb {
    background-color: var(--primary); /* .dark uses --primary directly */
    border: 2px solid var(--secondary); /* .dark uses --secondary directly */
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-accent);
}

.dark ::-webkit-scrollbar-thumb:hover {
    background-color: var(--accent); /* .dark uses --accent directly */
}
